import { qsFormInjectionKey } from '@sa/components/common/questions/qs-provide/context'

interface Options {
  disabled: Ref<boolean | null | undefined>
}

export default function useQuestionsForm(options: Options) {
  // 选中的选项
  const QForm = inject(qsFormInjectionKey, null)

  const disabled = computed(() => options.disabled.value)

  const mergedDisabled = computed(() => {
    if (disabled.value !== undefined && disabled.value !== null)
      return disabled.value
    return QForm?.props?.disabled ?? false
  })

  return {
    mergedDisabled,
  }
}
